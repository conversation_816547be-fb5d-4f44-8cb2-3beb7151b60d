package predict

import (
	"fmt"
	"rtb_model_server/internal/zaplog"
	"strconv"

	"os"

	"go.uber.org/zap"
	"gopkg.in/yaml.v2"
)

// FeatureConfig 特征配置结构
type FeatureConfig struct {
	Types    map[string]TypeConfig    `yaml:"types"`
	Features map[string]FeatureDetail `yaml:"features"`
}

type TypeConfig struct {
	Dtype   string      `yaml:"dtype"`
	Default interface{} `yaml:"default"`
}

type FeatureDetail struct {
	SlotID int    `yaml:"slot_id"`
	Field  string `yaml:"field"`
	Type   string `yaml:"type"`
	Desc   string `yaml:"desc"`
}

// FeatureProcessor 特征处理器
type FeatureProcessor struct {
	config *FeatureConfig
}

// NewFeatureProcessor 创建特征处理器
func NewFeatureProcessor(configPath string) (*FeatureProcessor, error) {
	config, err := loadFeatureConfig(configPath)
	if err != nil {
		zaplog.Logger.Error("Failed to load feature config",
			zap.String("path", configPath),
			zap.Error(err))
		return nil, fmt.Errorf("load feature config failed: %w", err)
	}

	zaplog.Logger.Info("Feature processor initialized",
		zap.String("config_path", configPath),
		zap.Int("feature_count", len(config.Features)),
		zap.Int("type_count", len(config.Types)))

	return &FeatureProcessor{config: config}, nil
}

// loadFeatureConfig 加载特征配置
func loadFeatureConfig(configPath string) (*FeatureConfig, error) {
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("read config file failed: %w", err)
	}

	var config FeatureConfig
	if err := yaml.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("parse yaml config failed: %w", err)
	}

	return &config, nil
}

// ProcessFeatures 处理特征数据
func (fp *FeatureProcessor) ProcessFeatures(features map[string]interface{}) (map[string]interface{}, error) {
	result := make(map[string]interface{})

	// 处理hash特征
	hashFeatures := make([]int64, 0)
	rawFeatures := make([]int64, 0)

	for featureName, featureDetail := range fp.config.Features {
		value, exists := features[featureName]
		if !exists {
			// 使用默认值
			if typeConfig, ok := fp.config.Types[featureDetail.Type]; ok {
				value = typeConfig.Default
			} else {
				zaplog.Logger.Debug("Feature not found and no default",
					zap.String("feature", featureName))
				continue
			}
		}

		if featureDetail.SlotID > 0 {
			// 需要hash的特征
			featureID, err := fp.calculateFeatureID(featureName, value, featureDetail.SlotID)
			if err != nil {
				zaplog.Logger.Error("Failed to calculate feature ID",
					zap.String("feature", featureName),
					zap.Error(err))
				continue
			}
			hashFeatures = append(hashFeatures, featureID)
		} else {
			// 原始特征
			int64Value, err := fp.convertToInt64(value)
			if err != nil {
				zaplog.Logger.Error("Failed to convert feature to int64",
					zap.String("feature", featureName),
					zap.Error(err))
				continue
			}
			rawFeatures = append(rawFeatures, int64Value)
		}
	}

	if len(hashFeatures) > 0 {
		result["hashed_features"] = hashFeatures
	}
	if len(rawFeatures) > 0 {
		result["raw_features"] = rawFeatures
	}

	zaplog.Logger.Debug("Features processed",
		zap.Int("hash_features", len(hashFeatures)),
		zap.Int("raw_features", len(rawFeatures)))

	return result, nil
}

// calculateFeatureID 计算特征ID - 使用专业的签名算法
func (fp *FeatureProcessor) calculateFeatureID(featureName string, value interface{}, slotID int) (int64, error) {
	// 将特征值转换为字符串进行hash
	var valueStr string
	switch v := value.(type) {
	case string:
		valueStr = v
	case int, int32, int64:
		valueStr = fmt.Sprintf("%d", v)
	case float32, float64:
		valueStr = fmt.Sprintf("%.6f", v)
	default:
		valueStr = fmt.Sprintf("%v", v)
	}

	// 使用专业的签名算法计算hash
	hash := Get48SignFromInput(valueStr)
	featureID := (int64(slotID) << 48) | (int64(hash) & 0xFFFFFFFFFFFF)

	return featureID, nil
}

// convertToInt64 转换为int64
func (fp *FeatureProcessor) convertToInt64(value interface{}) (int64, error) {
	switch v := value.(type) {
	case int:
		return int64(v), nil
	case int32:
		return int64(v), nil
	case int64:
		return v, nil
	case float32:
		return int64(v), nil
	case float64:
		return int64(v), nil
	case string:
		if parsed, err := strconv.ParseInt(v, 10, 64); err == nil {
			return parsed, nil
		}
		return 0, fmt.Errorf("cannot convert string to int64: %s", v)
	default:
		return 0, fmt.Errorf("unsupported type: %T", value)
	}
}

// GetFeatureCount 获取特征数量
func (fp *FeatureProcessor) GetFeatureCount() int {
	return len(fp.config.Features)
}

// GetHashFeatureCount 获取hash特征数量
func (fp *FeatureProcessor) GetHashFeatureCount() int {
	count := 0
	for _, feature := range fp.config.Features {
		if feature.SlotID > 0 {
			count++
		}
	}
	return count
}

// GetRawFeatureCount 获取原始特征数量
func (fp *FeatureProcessor) GetRawFeatureCount() int {
	count := 0
	for _, feature := range fp.config.Features {
		if feature.SlotID == 0 {
			count++
		}
	}
	return count
}
