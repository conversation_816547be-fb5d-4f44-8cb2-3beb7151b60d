package predict

import (
	"context"
	"rtb_model_server/common/domob_thrift/predict_model_server"
	"rtb_model_server/conf"
	ctx "rtb_model_server/internal/context"
	"rtb_model_server/internal/zaplog"
	"sync"
	"time"

	"go.uber.org/zap"
)

type PredictProcessor struct {
	tfServingClient *TFServingClient // gRPC客户端替换Thrift连接池
	monitorStop     chan struct{}
	monitorWg       sync.WaitGroup
}

func NewPredictProcessor() *PredictProcessor {
	config := conf.GlobalConfig.PredictServer

	// 创建TensorFlow Serving gRPC客户端
	tfServingClient, err := NewTFServingClient(config.Addr, config.ModelName)
	if err != nil {
		zaplog.Logger.Fatal("Failed to create TensorFlow Serving client", zap.Error(err))
	}

	p := &PredictProcessor{
		tfServingClient: tfServingClient,
		monitorStop:     make(chan struct{}),
	}

	// 启动监控协程
	p.startPoolMonitor()

	return p
}

// Close 关闭gRPC客户端
func (p *PredictProcessor) Close() {
	// 停止监控协程
	close(p.monitorStop)
	p.monitorWg.Wait()

	// 关闭gRPC连接
	if p.tfServingClient != nil {
		p.tfServingClient.Close()
	}
}

// GetPoolStats 获取gRPC连接统计信息
func (p *PredictProcessor) GetPoolStats() (total, inUse, idle int) {
	// gRPC连接不需要连接池统计，返回固定值
	return 1, 1, 0
}

// startPoolMonitor 启动连接池监控协程
func (p *PredictProcessor) startPoolMonitor() {
	p.monitorWg.Add(1)
	go func() {
		defer p.monitorWg.Done()

		// 监控间隔，可以根据需要调整
		monitorInterval := 30 * time.Second
		ticker := time.NewTicker(monitorInterval)
		defer ticker.Stop()

		zaplog.Logger.Info("Connection pool monitor started")

		for {
			select {
			case <-p.monitorStop:
				zaplog.Logger.Info("Connection pool monitor stopped")
				return
			case <-ticker.C:
				// 获取连接池统计信息
				total, inUse, idle := p.GetPoolStats()
				zaplog.Logger.Info("Connection Pool Stats", zap.Int("total", total), zap.Int("inUse", inUse), zap.Int("idle", idle))

				// 可以在这里添加更多监控逻辑，比如:
				// - 检查连接池使用率是否过高
				// - 记录监控指标到监控系统
				// - 在连接池耗尽时发出告警
				if total > 0 {
					usageRate := float64(inUse) / float64(total) * 100
					if usageRate > 80 {
						zaplog.Logger.Warn("WARNING: Connection pool usage rate is high", zap.Float64("usageRate", usageRate))
					}
				}
			}
		}
	}()
}

type PredictResult struct {
	CreativeId int32
	PreCtr     int64
	PreCvr     int64
	PreDeepCvr int64
}

// 调用预估服务，返回预估结果 - 混合架构：保持Thrift接口，内部使用gRPC
func (p *PredictProcessor) Predict(requestCtx *ctx.RequestContext, creativeIds []int32) (map[int32]PredictResult, error) {
	// 1. 从Thrift请求中提取特征
	features, err := p.extractFeaturesFromThriftRequest(requestCtx, creativeIds)
	if err != nil {
		return nil, err
	}

	// 2. 调用gRPC预测
	grpcResponse, err := p.tfServingClient.Predict(context.Background(), features)
	if err != nil {
		return nil, err
	}

	// 3. 转换回Thrift格式
	thriftResults := p.convertToThriftResults(grpcResponse, creativeIds)
	return thriftResults, nil
}
func (p *PredictProcessor) buildResponse(predictRsp *predict_model_server.PredictModelServerResponse) map[int32]PredictResult {
	results := make(map[int32]PredictResult)
	for _, ad := range predictRsp.ResponseList {

		results[int32(ad.Cid)] = PredictResult{
			CreativeId: int32(ad.Cid),
			PreCtr:     ad.Ctr,
			PreCvr:     ad.Cvr,
			PreDeepCvr: ad.DeepCvr,
		}
	}
	return results
}

func (p *PredictProcessor) buildRequestHeader(requestCtx *ctx.RequestContext, creativeIds []int32) *predict_model_server.PredictModelServerRequest {
	bidReq := requestCtx.BidRequest
	now := time.Now()
	req := &predict_model_server.PredictModelServerRequest{
		ReqId:         bidReq.ReqId,
		ReqTs:         int32(now.Unix()),
		ExchangeId:    bidReq.ExchangeId,
		SearchId:      bidReq.SearchId,
		User:          bidReq.User,
		Device:        bidReq.Device,
		App:           bidReq.App,
		AdxExchangeId: bidReq.AdxExchangeId,
		ModelName:     conf.GlobalConfig.PredictServer.ModelName,
	}
	req.AdList = p.buildAdList(requestCtx, creativeIds)
	return req
}

// 这里注意下，如果需要预估的创意太多，会导致预估超时，
func (p *PredictProcessor) buildAdList(requestCtx *ctx.RequestContext, creativeIds []int32) []*predict_model_server.PredictAdInfo {
	var index int32
	result := make([]*predict_model_server.PredictAdInfo, 0)
	for _, cid := range creativeIds {
		creative, err := requestCtx.AdIndex.GetCreative(cid)
		if err != nil {
			continue
		}
		tracking, err := requestCtx.AdIndex.GetAdTracking(creative.AdTrackingIds[0])
		if err != nil {
			continue
		}
		strategy, err := requestCtx.AdIndex.GetStrategy(creative.StrategyId)
		if err != nil {
			continue
		}
		ad := &predict_model_server.PredictAdInfo{
			AdIndex:    index,
			CreativeId: cid,
			SponsorId:  creative.SponsorId,
			CampaignId: creative.CampaignId,
			StrategyId: creative.StrategyId,
			TemplateId: creative.TemplateId,
			CostType:   strategy.MediaCostType,
			Bid:        strategy.Price,
			BidType:    strategy.MediaBidType,
			ProductId:  tracking.ProductId,
		}
		result = append(result, ad)
		index++

	}
	return result
}

// convertIPToInt64 将IP地址转换为int64 - 使用专业签名算法
func (p *PredictProcessor) convertIPToInt64(ip string) int64 {
	if ip == "" {
		return 0
	}
	// 使用专业的签名算法转换IP地址
	hash := Get48SignFromInput(ip)
	return int64(hash)
}

// convertStringToInt64 将字符串转换为int64 - 使用专业签名算法
func (p *PredictProcessor) convertStringToInt64(s string) int64 {
	if s == "" {
		return 0
	}
	// 使用专业的签名算法转换字符串
	hash := Get48SignFromInput(s)
	return int64(hash)
}
